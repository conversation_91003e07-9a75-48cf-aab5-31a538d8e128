# group/features/collaborative_writing.py
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QTextEdit, QListWidget, QListWidgetItem,
                             QSplitter, QFrame, QScrollArea, QFileDialog,
                             QMessageBox, QDialog, QDialogButtonBox, QLineEdit,
                             QComboBox, QGroupBox, QToolBar, QAction, QColorDialog,
                             QFontDialog, QMenu, QToolButton, QSpinBox, QInputDialog)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QPoint, QRect, QBuffer, QByteArray, QIODevice
from PyQt5.QtGui import (QFont, QPixmap, QIcon, QPainter, QPen, QColor, 
                         QBrush, QPainterPath, QImage, QCursor, QDrag, QKeySequence)
import os
import json
import time
import uuid
from datetime import datetime
import base64
from enum import Enum
from features.collaborative_whiteboard import CollaborativeWhiteboardManager

class ContentType(Enum):
    """内容类型"""
    TEXT = "text"
    DRAWING = "drawing"
    IMAGE = "image"
    SHAPE = "shape"

class ToolType(Enum):
    """工具类型"""
    SELECT = "select"
    PEN = "pen"
    ERASER = "eraser"
    TEXT = "text"
    IMAGE = "image"
    SHAPE = "shape"

class ShapeType(Enum):
    """形状类型"""
    RECTANGLE = "rectangle"
    ELLIPSE = "ellipse"
    LINE = "line"
    ARROW = "arrow"

class ContentItem:
    """内容项"""
    def __init__(self, item_id, content_type, user_id, user_name, data, position, size=None, z_index=0):
        self.id = item_id or str(uuid.uuid4())
        self.content_type = content_type
        self.user_id = user_id
        self.user_name = user_name
        self.data = data
        self.position = position  # (x, y)
        self.size = size or (100, 100)  # (width, height)
        self.z_index = z_index
        self.created_at = time.time()
        self.updated_at = time.time()
        self.is_selected = False
        
    def to_dict(self):
        return {
            'id': self.id,
            'content_type': self.content_type.value if isinstance(self.content_type, Enum) else self.content_type,
            'user_id': self.user_id,
            'user_name': self.user_name,
            'data': self.data,
            'position': self.position,
            'size': self.size,
            'z_index': self.z_index,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def from_dict(cls, data):
        content_type = data['content_type']
        if isinstance(content_type, str):
            try:
                content_type = ContentType(content_type)
            except ValueError:
                pass
                
        return cls(
            data['id'],
            content_type,
            data['user_id'],
            data['user_name'],
            data['data'],
            data['position'],
            data.get('size'),
            data.get('z_index', 0)
        )
    
    def contains_point(self, point):
        """检查点是否在内容项内"""
        x, y = point
        pos_x, pos_y = self.position
        width, height = self.size
        
        return pos_x <= x <= pos_x + width and pos_y <= y <= pos_y + height
class CollaborativeWritingCanvas(QWidget):
    """协同书写画布"""
    
    # 信号定义
    content_added = pyqtSignal(dict)  # 添加内容
    content_modified = pyqtSignal(dict)  # 修改内容
    content_deleted = pyqtSignal(dict)  # 删除内容
    content_selected = pyqtSignal(dict)  # 选择内容
    canvas_cleared = pyqtSignal()  # 清空画布
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.contents = []  # 内容项列表
        self.current_tool = ToolType.SELECT
        self.current_shape = ShapeType.RECTANGLE
        self.current_color = QColor(0, 0, 0)
        self.current_font = QFont("Arial", 12)
        self.current_line_width = 2
        
        self.is_drawing = False
        self.drawing_path = QPainterPath()
        self.drawing_points = []
        self.start_pos = None
        self.current_pos = None
        
        self.selected_item = None
        self.drag_start_pos = None
        self.drag_offset = None
        
        self.clipboard = None
        
        # 设置鼠标跟踪
        self.setMouseTracking(True)
        self.setFocusPolicy(Qt.StrongFocus)
        
        # 设置背景色
        self.setAutoFillBackground(True)
        palette = self.palette()
        palette.setColor(self.backgroundRole(), Qt.white)
        self.setPalette(palette)
        
        # 设置最小尺寸
        self.setMinimumSize(800, 600)
        
    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制所有内容项
        for item in sorted(self.contents, key=lambda x: x.z_index):
            self.paint_item(painter, item)
            
        # 绘制当前绘制的路径
        if self.is_drawing and self.current_tool == ToolType.PEN:
            painter.setPen(QPen(self.current_color, self.current_line_width, Qt.SolidLine, Qt.RoundCap, Qt.RoundJoin))
            painter.drawPath(self.drawing_path)
            
        # 绘制当前形状预览
        if self.is_drawing and self.current_tool == ToolType.SHAPE and self.start_pos and self.current_pos:
            painter.setPen(QPen(self.current_color, self.current_line_width, Qt.SolidLine))
            
            if self.current_shape == ShapeType.RECTANGLE:
                painter.drawRect(QRect(self.start_pos, self.current_pos))
            elif self.current_shape == ShapeType.ELLIPSE:
                painter.drawEllipse(QRect(self.start_pos, self.current_pos))
            elif self.current_shape == ShapeType.LINE:
                painter.drawLine(self.start_pos, self.current_pos)
            elif self.current_shape == ShapeType.ARROW:
                self.draw_arrow(painter, self.start_pos, self.current_pos)
                
        # 绘制选中项的边框
        if self.selected_item:
            painter.setPen(QPen(Qt.blue, 1, Qt.DashLine))
            x, y = self.selected_item.position
            w, h = self.selected_item.size
            painter.drawRect(x-2, y-2, w+4, h+4)
            
            # 绘制控制点
            painter.setBrush(QBrush(Qt.blue))
            painter.setPen(QPen(Qt.white, 1))
            
            # 四个角和四个边的中点
            control_points = [
                (x, y),             # 左上
                (x + w//2, y),      # 上中
                (x + w, y),         # 右上
                (x + w, y + h//2),  # 右中
                (x + w, y + h),     # 右下
                (x + w//2, y + h),  # 下中
                (x, y + h),         # 左下
                (x, y + h//2)       # 左中
            ]
            
            for point in control_points:
                painter.drawRect(point[0] - 3, point[1] - 3, 6, 6)
                
    def paint_item(self, painter, item):
        """绘制内容项"""
        x, y = item.position
        w, h = item.size
        
        if item.content_type == ContentType.TEXT:
            painter.setFont(QFont(item.data.get('font_family', 'Arial'), 
                                 item.data.get('font_size', 12)))
            painter.setPen(QColor(item.data.get('color', '#000000')))
            painter.drawText(QRect(x, y, w, h), Qt.TextWordWrap, item.data.get('text', ''))
            
        elif item.content_type == ContentType.DRAWING:
            path = QPainterPath()
            points = item.data.get('points', [])
            if points:
                path.moveTo(points[0][0], points[0][1])
                for point in points[1:]:
                    path.lineTo(point[0], point[1])
                    
                painter.setPen(QPen(QColor(item.data.get('color', '#000000')), 
                                   item.data.get('line_width', 2),
                                   Qt.SolidLine, Qt.RoundCap, Qt.RoundJoin))
                painter.drawPath(path)
                
        elif item.content_type == ContentType.IMAGE:
            image_data = item.data.get('image_data', '')
            if image_data:
                image = QImage()
                image.loadFromData(base64.b64decode(image_data))
                painter.drawImage(QRect(x, y, w, h), image)
                
        elif item.content_type == ContentType.SHAPE:
            painter.setPen(QPen(QColor(item.data.get('color', '#000000')), 
                               item.data.get('line_width', 2)))
            
            shape_type = item.data.get('shape_type', 'rectangle')
            
            if shape_type == 'rectangle':
                painter.drawRect(x, y, w, h)
            elif shape_type == 'ellipse':
                painter.drawEllipse(x, y, w, h)
            elif shape_type == 'line':
                start = item.data.get('start', (x, y))
                end = item.data.get('end', (x + w, y + h))
                painter.drawLine(start[0], start[1], end[0], end[1])
            elif shape_type == 'arrow':
                start = item.data.get('start', (x, y))
                end = item.data.get('end', (x + w, y + h))
                self.draw_arrow(painter, QPoint(start[0], start[1]), QPoint(end[0], end[1]))
                
    def draw_arrow(self, painter, start, end):
        """绘制箭头"""
        painter.drawLine(start, end)
        
        # 计算箭头
        angle = 30  # 箭头角度
        arrow_length = 15  # 箭头长度
        
        # 计算方向向量
        dx = end.x() - start.x()
        dy = end.y() - start.y()
        length = (dx**2 + dy**2)**0.5
        
        if length == 0:
            return
            
        # 单位向量
        dx, dy = dx / length, dy / length
        
        # 计算箭头两个点
        x1 = end.x() - arrow_length * (dx * 0.866 + dy * 0.5)
        y1 = end.y() - arrow_length * (dy * 0.866 - dx * 0.5)
        
        x2 = end.x() - arrow_length * (dx * 0.866 - dy * 0.5)
        y2 = end.y() - arrow_length * (dy * 0.866 + dx * 0.5)
        
        # 绘制箭头
        painter.drawLine(end, QPoint(int(x1), int(y1)))
        painter.drawLine(end, QPoint(int(x2), int(y2)))    
    def mousePressEvent(self, event):
            """鼠标按下事件"""
            if event.button() == Qt.LeftButton:
                pos = event.pos()
                self.start_pos = pos
                self.current_pos = pos
                
                if self.current_tool == ToolType.SELECT:
                    # 检查是否点击了某个内容项
                    clicked_item = self.get_item_at(pos)
                    
                    # 取消之前的选择
                    if self.selected_item:
                        self.selected_item.is_selected = False
                    
                    self.selected_item = clicked_item
                    
                    if clicked_item:
                        clicked_item.is_selected = True
                        self.content_selected.emit(clicked_item.to_dict())
                        
                        # 准备拖动
                        self.drag_start_pos = pos
                        self.drag_offset = (pos.x() - clicked_item.position[0], 
                                        pos.y() - clicked_item.position[1])
                    
                    self.update()
                    
                elif self.current_tool == ToolType.PEN:
                    self.is_drawing = True
                    self.drawing_path = QPainterPath()
                    self.drawing_path.moveTo(pos)
                    self.drawing_points = [(pos.x(), pos.y())]
                    
                elif self.current_tool == ToolType.ERASER:
                    # 查找并删除点击位置的内容
                    item = self.get_item_at(pos)
                    if item:
                        self.delete_item(item)
                        
                elif self.current_tool == ToolType.TEXT:
                    # 创建文本输入对话框
                    text, ok = QInputDialog.getText(self, "输入文本", "请输入文本内容:")
                    if ok and text:
                        self.add_text(text, (pos.x(), pos.y()))
                        
                elif self.current_tool == ToolType.IMAGE:
                    # 打开文件对话框选择图片
                    file_path, _ = QFileDialog.getOpenFileName(
                        self, "选择图片", "", "图片文件 (*.jpg *.jpeg *.png *.gif *.bmp)"
                    )
                    
                    if file_path:
                        self.add_image(file_path, (pos.x(), pos.y()))
                        
                elif self.current_tool == ToolType.SHAPE:
                    self.is_drawing = True
                
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        pos = event.pos()
        self.current_pos = pos
        
        if event.buttons() & Qt.LeftButton:
            if self.current_tool == ToolType.SELECT and self.selected_item and self.drag_start_pos:
                # 拖动选中的内容
                new_x = pos.x() - self.drag_offset[0]
                new_y = pos.y() - self.drag_offset[1]
                
                self.selected_item.position = (new_x, new_y)
                self.selected_item.updated_at = time.time()
                
                self.update()
                
            elif self.current_tool == ToolType.PEN and self.is_drawing:
                # 绘制路径
                self.drawing_path.lineTo(pos)
                self.drawing_points.append((pos.x(), pos.y()))
                self.update()
                
            elif self.current_tool == ToolType.SHAPE and self.is_drawing:
                self.update()
                
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            if self.current_tool == ToolType.SELECT and self.selected_item:
                # 完成拖动，发送修改信号
                self.content_modified.emit(self.selected_item.to_dict())
                
            elif self.current_tool == ToolType.PEN and self.is_drawing:
                # 完成绘制，添加到内容列表
                if len(self.drawing_points) > 1:
                    self.add_drawing(self.drawing_points)
                
                self.is_drawing = False
                self.drawing_path = QPainterPath()
                self.drawing_points = []
                self.update()
                
            elif self.current_tool == ToolType.SHAPE and self.is_drawing:
                # 完成形状绘制
                if self.start_pos and self.current_pos:
                    x1, y1 = self.start_pos.x(), self.start_pos.y()
                    x2, y2 = self.current_pos.x(), self.current_pos.y()
                    
                    # 确保x1,y1是左上角，x2,y2是右下角
                    x = min(x1, x2)
                    y = min(y1, y2)
                    w = abs(x2 - x1)
                    h = abs(y2 - y1)
                    
                    if w > 5 and h > 5:  # 忽略太小的形状
                        if self.current_shape == ShapeType.RECTANGLE:
                            self.add_shape('rectangle', (x, y), (w, h))
                        elif self.current_shape == ShapeType.ELLIPSE:
                            self.add_shape('ellipse', (x, y), (w, h))
                        elif self.current_shape == ShapeType.LINE:
                            self.add_shape('line', (x1, y1), (x2, y2))
                        elif self.current_shape == ShapeType.ARROW:
                            self.add_shape('arrow', (x1, y1), (x2, y2))
                
                self.is_drawing = False
                self.start_pos = None
                self.current_pos = None
                self.update()
                
            self.drag_start_pos = None
            
    def keyPressEvent(self, event):
        """键盘按下事件"""
        if event.key() == Qt.Key_Delete and self.selected_item:
            # 删除选中的内容
            self.delete_item(self.selected_item)
            
        elif event.matches(QKeySequence.Copy) and self.selected_item:
            # 复制选中的内容
            self.clipboard = self.selected_item
            
        elif event.matches(QKeySequence.Paste) and self.clipboard:
            # 粘贴内容
            new_item = ContentItem(
                None,  # 新ID
                self.clipboard.content_type,
                self.clipboard.user_id,
                self.clipboard.user_name,
                self.clipboard.data.copy(),
                (self.clipboard.position[0] + 20, self.clipboard.position[1] + 20),
                self.clipboard.size,
                len(self.contents)  # 置于顶层
            )
            
            self.contents.append(new_item)
            self.content_added.emit(new_item.to_dict())
            
            # 选中新粘贴的内容
            if self.selected_item:
                self.selected_item.is_selected = False
            self.selected_item = new_item
            self.selected_item.is_selected = True
            
            self.update()
            
    def get_item_at(self, pos):
        """获取指定位置的内容项"""
        # 从上到下（z-index从大到小）检查
        for item in sorted(self.contents, key=lambda x: x.z_index, reverse=True):
            if item.contains_point((pos.x(), pos.y())):
                return item
        return None
        
    def add_text(self, text, position):
        """添加文本"""
        item = ContentItem(
            None,
            ContentType.TEXT,
            "local_user",  # 这里应该使用实际的用户ID
            "Local User",  # 这里应该使用实际的用户名
            {
                'text': text,
                'font_family': self.current_font.family(),
                'font_size': self.current_font.pointSize(),
                'color': self.current_color.name()
            },
            position,
            (200, 100),  # 默认大小
            len(self.contents)  # z-index
        )
        
        self.contents.append(item)
        self.content_added.emit(item.to_dict())
        self.update()
        
    def add_drawing(self, points):
        """添加绘图"""
        # 计算绘图的边界框
        x_coords = [p[0] for p in points]
        y_coords = [p[1] for p in points]
        
        min_x, max_x = min(x_coords), max(x_coords)
        min_y, max_y = min(y_coords), max(y_coords)
        
        width = max_x - min_x
        height = max_y - min_y
        
        # 相对于边界框的点
        relative_points = [(p[0] - min_x, p[1] - min_y) for p in points]
        
        item = ContentItem(
            None,
            ContentType.DRAWING,
            "local_user",  # 这里应该使用实际的用户ID
            "Local User",  # 这里应该使用实际的用户名
            {
                'points': relative_points,
                'color': self.current_color.name(),
                'line_width': self.current_line_width
            },
            (min_x, min_y),
            (width, height),
            len(self.contents)  # z-index
        )
        
        self.contents.append(item)
        self.content_added.emit(item.to_dict())
        self.update()   
    def add_image(self, file_path, position):
            """添加图片"""
            image = QImage(file_path)
            if image.isNull():
                return
                
            # 调整图片大小，保持比例
            max_size = 400
            width, height = image.width(), image.height()
            
            if width > max_size or height > max_size:
                if width > height:
                    height = int(height * max_size / width)
                    width = max_size
                else:
                    width = int(width * max_size / height)
                    height = max_size
                    
            # 转换为base64
            byte_array = QByteArray()
            buffer = QBuffer(byte_array)
            buffer.open(QIODevice.WriteOnly)
            image.scaled(width, height, Qt.KeepAspectRatio, Qt.SmoothTransformation).save(buffer, "PNG")
            image_data = base64.b64encode(byte_array.data()).decode('utf-8')
            
            item = ContentItem(
                None,
                ContentType.IMAGE,
                "local_user",  # 这里应该使用实际的用户ID
                "Local User",  # 这里应该使用实际的用户名
                {
                    'image_data': image_data,
                    'original_path': file_path
                },
                position,
                (width, height),
                len(self.contents)  # z-index
            )
            
            self.contents.append(item)
            self.content_added.emit(item.to_dict())
            self.update()
            
    def add_shape(self, shape_type, start, end=None):
        """添加形状"""
        if shape_type in ['rectangle', 'ellipse']:
            # start是左上角坐标，end是宽高
            item = ContentItem(
                None,
                ContentType.SHAPE,
                "local_user",  # 这里应该使用实际的用户ID
                "Local User",  # 这里应该使用实际的用户名
                {
                    'shape_type': shape_type,
                    'color': self.current_color.name(),
                    'line_width': self.current_line_width
                },
                start,
                end,
                len(self.contents)  # z-index
            )
        else:  # line, arrow
            # start和end是两个点的坐标
            x1, y1 = start
            x2, y2 = end
            
            # 计算边界框
            x = min(x1, x2)
            y = min(y1, y2)
            w = abs(x2 - x1)
            h = abs(y2 - y1)
            
            item = ContentItem(
                None,
                ContentType.SHAPE,
                "local_user",  # 这里应该使用实际的用户ID
                "Local User",  # 这里应该使用实际的用户名
                {
                    'shape_type': shape_type,
                    'start': start,
                    'end': end,
                    'color': self.current_color.name(),
                    'line_width': self.current_line_width
                },
                (x, y),
                (w, h),
                len(self.contents)  # z-index
            )
            
        self.contents.append(item)
        self.content_added.emit(item.to_dict())
        self.update()
        
    def delete_item(self, item):
        """删除内容项"""
        if item in self.contents:
            self.contents.remove(item)
            self.content_deleted.emit(item.to_dict())
            
            if self.selected_item == item:
                self.selected_item = None
                
            self.update()
            
    def clear_canvas(self):
        """清空画布"""
        self.contents = []
        self.selected_item = None
        self.canvas_cleared.emit()
        self.update()
        
    def set_tool(self, tool):
        """设置当前工具"""
        self.current_tool = tool
        
        # 如果切换到非选择工具，取消当前选择
        if tool != ToolType.SELECT and self.selected_item:
            self.selected_item.is_selected = False
            self.selected_item = None
            self.update()
            
    def set_shape(self, shape):
        """设置当前形状"""
        self.current_shape = shape
        
    def set_color(self, color):
        """设置当前颜色"""
        self.current_color = color
        
    def set_font(self, font):
        """设置当前字体"""
        self.current_font = font
        
    def set_line_width(self, width):
        """设置当前线宽"""
        self.current_line_width = width
        
    def add_remote_content(self, content_data):
        """添加远程内容"""
        item = ContentItem.from_dict(content_data)
        self.contents.append(item)
        self.update()
        
    def update_remote_content(self, content_data):
        """更新远程内容"""
        item_id = content_data['id']
        
        for i, item in enumerate(self.contents):
            if item.id == item_id:
                self.contents[i] = ContentItem.from_dict(content_data)
                self.update()
                break
                
    def delete_remote_content(self, content_data):
        """删除远程内容"""
        item_id = content_data['id']
        
        for item in self.contents[:]:
            if item.id == item_id:
                self.contents.remove(item)
                
                if self.selected_item == item:
                    self.selected_item = None
                    
                self.update()
                break
                
    def get_canvas_state(self):
        """获取画布状态"""
        return [item.to_dict() for item in self.contents]
        
    def load_canvas_state(self, state):
        """加载画布状态"""
        self.contents = [ContentItem.from_dict(item_data) for item_data in state]
        self.selected_item = None
        self.update()
        
    def save_to_image(self, file_path):
        """保存为图片"""
        image = QImage(self.size(), QImage.Format_ARGB32)
        image.fill(Qt.white)
        
        painter = QPainter(image)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制所有内容项
        for item in sorted(self.contents, key=lambda x: x.z_index):
            self.paint_item(painter, item)
            
        painter.end()
        
        return image.save(file_path)

class CollaborativeWritingWidget(QWidget):
    """协同书写组件"""
    
    # 信号定义
    content_shared = pyqtSignal(dict)  # 内容被分享
    
    def __init__(self, socket_manager=None, group_id=None, user_id=None, user_name=None, parent=None):
        super().__init__(parent)
        self.socket_manager = socket_manager
        self.group_id = group_id or "default_group"
        self.user_id = user_id or "local_user"
        self.user_name = user_name or "Local User"
        
        # 创建协同管理器
        self.collaborative_manager = CollaborativeWhiteboardManager(
            socket_manager, self.group_id, self.user_id, self.user_name, self
        )
        
        self.initUI()
        self.setup_connections()
        
    def initUI(self):
        """初始化用户界面"""
        self.setWindowTitle("协同书写")
        self.setMinimumSize(1000, 700)
        
        main_layout = QVBoxLayout(self)
        
        # 工具栏
        self.toolbar = QToolBar()
        self.toolbar.setIconSize(QSize(24, 24))
        
        # 选择工具
        self.select_action = QAction(QIcon.fromTheme("edit-select"), "选择", self)
        self.select_action.setCheckable(True)
        self.select_action.setChecked(True)
        self.toolbar.addAction(self.select_action)
        
        # 绘制工具
        self.pen_action = QAction(QIcon.fromTheme("draw-freehand"), "画笔", self)
        self.pen_action.setCheckable(True)
        self.toolbar.addAction(self.pen_action)
        
        # 橡皮擦工具
        self.eraser_action = QAction(QIcon.fromTheme("draw-eraser"), "橡皮擦", self)
        self.eraser_action.setCheckable(True)
        self.toolbar.addAction(self.eraser_action)
        
        # 文本工具
        self.text_action = QAction(QIcon.fromTheme("insert-text"), "文本", self)
        self.text_action.setCheckable(True)
        self.toolbar.addAction(self.text_action)
        
        # 图片工具
        self.image_action = QAction(QIcon.fromTheme("insert-image"), "图片", self)
        self.image_action.setCheckable(True)
        self.toolbar.addAction(self.image_action)
        
        # 形状工具
        self.shape_action = QAction(QIcon.fromTheme("draw-rectangle"), "形状", self)
        self.shape_action.setCheckable(True)
        self.toolbar.addAction(self.shape_action)
        
        # 形状选择下拉菜单
        self.shape_button = QToolButton()
        self.shape_button.setIcon(QIcon.fromTheme("draw-rectangle"))
        self.shape_button.setPopupMode(QToolButton.InstantPopup)
        
        shape_menu = QMenu(self)
        
        self.rect_action = QAction(QIcon.fromTheme("draw-rectangle"), "矩形", self)
        self.rect_action.setCheckable(True)
        self.rect_action.setChecked(True)
        shape_menu.addAction(self.rect_action)
        
        self.ellipse_action = QAction(QIcon.fromTheme("draw-ellipse"), "椭圆", self)
        self.ellipse_action.setCheckable(True)
        shape_menu.addAction(self.ellipse_action)
        
        self.line_action = QAction(QIcon.fromTheme("draw-line"), "直线", self)
        self.line_action.setCheckable(True)
        shape_menu.addAction(self.line_action)
        
        self.arrow_action = QAction(QIcon.fromTheme("draw-arrow"), "箭头", self)
        self.arrow_action.setCheckable(True)
        shape_menu.addAction(self.arrow_action)
        
        self.shape_button.setMenu(shape_menu)
        self.toolbar.addWidget(self.shape_button)
        
        self.toolbar.addSeparator()
        
        # 颜色选择
        self.color_button = QPushButton()
        self.color_button.setFixedSize(24, 24)
        self.color_button.setStyleSheet("background-color: black;")
        self.toolbar.addWidget(self.color_button)
        
        # 线宽选择
        self.toolbar.addWidget(QLabel("线宽:"))
        self.line_width_spin = QSpinBox()
        self.line_width_spin.setRange(1, 20)
        self.line_width_spin.setValue(2)
        self.toolbar.addWidget(self.line_width_spin)
        
        # 字体选择
        self.font_button = QPushButton("字体...")
        self.toolbar.addWidget(self.font_button)
        
        self.toolbar.addSeparator()
        
        # 清空画布
        self.clear_action = QAction(QIcon.fromTheme("edit-clear"), "清空", self)
        self.toolbar.addAction(self.clear_action)
        
        # 保存图片
        self.save_action = QAction(QIcon.fromTheme("document-save"), "保存", self)
        self.toolbar.addAction(self.save_action)
        
        # 分享到学员端
        self.share_action = QAction(QIcon.fromTheme("document-send"), "分享到学员端", self)
        self.toolbar.addAction(self.share_action)
        
        main_layout.addWidget(self.toolbar)
        
        # 画布
        self.canvas = CollaborativeWritingCanvas()
        main_layout.addWidget(self.canvas)
        
        # 状态栏
        status_layout = QHBoxLayout()
        self.status_label = QLabel("就绪")
        self.user_label = QLabel(f"用户: {self.user_name}")
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.user_label)
        
        main_layout.addLayout(status_layout)
        
    def setup_connections(self):
        """设置信号连接"""
        # 工具选择
        self.select_action.triggered.connect(lambda: self.set_tool(ToolType.SELECT))
        self.pen_action.triggered.connect(lambda: self.set_tool(ToolType.PEN))
        self.eraser_action.triggered.connect(lambda: self.set_tool(ToolType.ERASER))
        self.text_action.triggered.connect(lambda: self.set_tool(ToolType.TEXT))
        self.image_action.triggered.connect(lambda: self.set_tool(ToolType.IMAGE))
        self.shape_action.triggered.connect(lambda: self.set_tool(ToolType.SHAPE))
        
        # 形状选择
        self.rect_action.triggered.connect(lambda: self.set_shape(ShapeType.RECTANGLE))
        self.ellipse_action.triggered.connect(lambda: self.set_shape(ShapeType.ELLIPSE))
        self.line_action.triggered.connect(lambda: self.set_shape(ShapeType.LINE))
        self.arrow_action.triggered.connect(lambda: self.set_shape(ShapeType.ARROW))
        
        # 颜色选择
        self.color_button.clicked.connect(self.choose_color)
        
        # 线宽选择
        self.line_width_spin.valueChanged.connect(self.canvas.set_line_width)
        
        # 字体选择
        self.font_button.clicked.connect(self.choose_font)
        
        # 清空画布
        self.clear_action.triggered.connect(self.canvas.clear_canvas)
        
        # 保存图片
        self.save_action.triggered.connect(self.save_canvas)
        
        # 分享到学员端
        self.share_action.triggered.connect(self.share_to_students)
        
        # 画布信号
        self.canvas.content_added.connect(self.on_content_added)
        self.canvas.content_modified.connect(self.on_content_modified)
        self.canvas.content_deleted.connect(self.on_content_deleted)
        self.canvas.content_selected.connect(self.on_content_selected)
        self.canvas.canvas_cleared.connect(self.on_canvas_cleared)
        
        # 协同管理器信号
        self.collaborative_manager.operation_received.connect(self.on_operation_received)
        self.collaborative_manager.user_joined.connect(self.on_user_joined)
        self.collaborative_manager.user_left.connect(self.on_user_left)
        
    def set_tool(self, tool):
        """设置当前工具"""
        self.canvas.set_tool(tool)
        
        # 更新工具栏状态
        self.select_action.setChecked(tool == ToolType.SELECT)
        self.pen_action.setChecked(tool == ToolType.PEN)
        self.eraser_action.setChecked(tool == ToolType.ERASER)
        self.text_action.setChecked(tool == ToolType.TEXT)
        self.image_action.setChecked(tool == ToolType.IMAGE)
        self.shape_action.setChecked(tool == ToolType.SHAPE)
        
        # 更新状态栏
        tool_names = {
            ToolType.SELECT: "选择",
            ToolType.PEN: "画笔",
            ToolType.ERASER: "橡皮擦",
            ToolType.TEXT: "文本",
            ToolType.IMAGE: "图片",
            ToolType.SHAPE: "形状"
        }
        self.status_label.setText(f"当前工具: {tool_names.get(tool, '未知')}")
        
    def set_shape(self, shape):
        """设置当前形状"""
        self.canvas.set_shape(shape)
        
        # 更新形状按钮图标
        shape_icons = {
            ShapeType.RECTANGLE: "draw-rectangle",
            ShapeType.ELLIPSE: "draw-ellipse",
            ShapeType.LINE: "draw-line",
            ShapeType.ARROW: "draw-arrow"
        }
        self.shape_button.setIcon(QIcon.fromTheme(shape_icons.get(shape, "draw-rectangle")))
        
        # 更新菜单状态
        self.rect_action.setChecked(shape == ShapeType.RECTANGLE)
        self.ellipse_action.setChecked(shape == ShapeType.ELLIPSE)
        self.line_action.setChecked(shape == ShapeType.LINE)
        self.arrow_action.setChecked(shape == ShapeType.ARROW)
        
    def choose_color(self):
        """选择颜色"""
        color = QColorDialog.getColor(self.canvas.current_color, self, "选择颜色")
        if color.isValid():
            self.canvas.set_color(color)
            self.color_button.setStyleSheet(f"background-color: {color.name()};")
            
    def choose_font(self):
        """选择字体"""
        font, ok = QFontDialog.getFont(self.canvas.current_font, self, "选择字体")
        if ok:
            self.canvas.set_font(font)
            
    def save_canvas(self):
        """保存画布为图片"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存图片", "", "PNG图片 (*.png);;JPEG图片 (*.jpg);;所有文件 (*.*)"
        )
        
        if file_path:
            if self.canvas.save_to_image(file_path):
                QMessageBox.information(self, "保存成功", f"画布已保存到 {file_path}")
            else:
                QMessageBox.warning(self, "保存失败", "保存画布失败")
                
    def share_to_students(self):
        """分享到学员端"""
        # 获取画布状态
        canvas_state = self.canvas.get_canvas_state()
        
        # 生成缩略图
        image = QImage(self.canvas.size(), QImage.Format_ARGB32)
        image.fill(Qt.white)
        
        painter = QPainter(image)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制所有内容项
        for item_data in canvas_state:
            item = ContentItem.from_dict(item_data)
            self.canvas.paint_item(painter, item)
            
        painter.end()
        
        # 转换为base64
        byte_array = QByteArray()
        buffer = QBuffer(byte_array)
        buffer.open(QIODevice.WriteOnly)
        image.scaled(400, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation).save(buffer, "PNG")
        thumbnail = base64.b64encode(byte_array.data()).decode('utf-8')
        
        # 创建分享数据
        share_data = {
            'type': 'collaborative_writing',
            'group_id': self.group_id,
            'user_id': self.user_id,
            'user_name': self.user_name,
            'timestamp': time.time(),
            'canvas_state': canvas_state,
            'thumbnail': thumbnail
        }
        
        # 发送分享信号
        self.content_shared.emit(share_data)
        
        # 如果有socket_manager，发送到服务器
        if self.socket_manager:
            self.socket_manager.send_event('share_collaborative_content', share_data)
            QMessageBox.information(self, "分享成功", "内容已分享到学员端")
        else:
            QMessageBox.warning(self, "分享失败", "未连接到服务器，无法分享内容")
            
    def on_content_added(self, content_data):
        """内容添加事件处理"""
        # 更新用户ID和用户名
        content_data['user_id'] = self.user_id
        content_data['user_name'] = self.user_name
        
        # 发送到协同管理器
        self.collaborative_manager.add_local_operation('add_content', content_data)
        
    def on_content_modified(self, content_data):
        """内容修改事件处理"""
        # 发送到协同管理器
        self.collaborative_manager.add_local_operation('modify_content', content_data)
        
    def on_content_deleted(self, content_data):
        """内容删除事件处理"""
        # 发送到协同管理器
        self.collaborative_manager.add_local_operation('delete_content', content_data)
        
    def on_content_selected(self, content_data):
        """内容选中事件处理"""
        # 这个事件只在本地处理，不需要同步
        pass
        
    def on_canvas_cleared(self):
        """画布清空事件处理"""
        # 发送到协同管理器
        self.collaborative_manager.add_local_operation('clear_canvas', {})
        
    def on_operation_received(self, operation_data):
        """接收到远程操作"""
        op_type = operation_data.get('type')
        data = operation_data.get('data', {})
        
        if op_type == 'add_content':
            self.canvas.add_remote_content(data)
        elif op_type == 'modify_content':
            self.canvas.update_remote_content(data)
        elif op_type == 'delete_content':
            self.canvas.delete_remote_content(data)
        elif op_type == 'clear_canvas':
            self.canvas.clear_canvas()
            
    def on_user_joined(self, user_id, user_name):
        """用户加入事件处理"""
        self.status_label.setText(f"用户 {user_name} 加入协作")
        
    def on_user_left(self, user_id):
        """用户离开事件处理"""
        self.status_label.setText(f"用户 {user_id} 离开协作")
        
    def load_shared_content(self, share_data):
        """加载分享的内容"""
        canvas_state = share_data.get('canvas_state', [])
        self.canvas.load_canvas_state(canvas_state)
        
        self.status_label.setText(f"已加载来自 {share_data.get('user_name', '未知用户')} 的分享内容")