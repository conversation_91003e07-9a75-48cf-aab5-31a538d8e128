# group/features/material_display.py
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QListWidget, QListWidgetItem,
                             QSplitter, QFrame, QScrollArea, QFileDialog,
                             QMessageBox, QDialog, QDialogButtonBox, QLineEdit,
                             QComboBox, QGroupBox, QGridLayout, QMenu)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QUrl, QFileInfo
from PyQt5.QtGui import QFont, QPixmap, QIcon, QImage
from PyQt5.QtMultimedia import QMediaContent, QMediaPlayer
from PyQt5.QtMultimediaWidgets import QVideoWidget
import os
import json
import time
from datetime import datetime
import mimetypes
import shutil

class MaterialType:
    """材料类型"""
    IMAGE = "image"
    DOCUMENT = "document"
    VIDEO = "video"
    AUDIO = "audio"
    URL = "url"
    TEXT = "text"

class StudentMaterial:
    """学生讨论材料"""
    def __init__(self, material_id, student_id, student_name, title, material_type, content_path, thumbnail_path=None, description=None):
        self.material_id = material_id
        self.student_id = student_id
        self.student_name = student_name
        self.title = title
        self.material_type = material_type
        self.content_path = content_path  # 文件路径或内容
        self.thumbnail_path = thumbnail_path  # 缩略图路径
        self.description = description or ""
        self.created_at = datetime.now()
        
    def to_dict(self):
        return {
            'material_id': self.material_id,
            'student_id': self.student_id,
            'student_name': self.student_name,
            'title': self.title,
            'material_type': self.material_type,
            'content_path': self.content_path,
            'thumbnail_path': self.thumbnail_path,
            'description': self.description,
            'created_at': self.created_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data):
        material = cls(
            data['material_id'],
            data['student_id'],
            data['student_name'],
            data['title'],
            data['material_type'],
            data['content_path'],
            data.get('thumbnail_path'),
            data.get('description', "")
        )
        if 'created_at' in data:
            material.created_at = datetime.fromisoformat(data['created_at'])
        return material

class MaterialDisplayWidget(QWidget):
    """材料展示组件"""
    
    # 信号定义
    material_selected = pyqtSignal(dict)  # 材料被选中
    material_shared = pyqtSignal(dict)    # 材料被分享
    
    def __init__(self, socket_manager=None, parent=None):
        super().__init__(parent)
        self.socket_manager = socket_manager
        self.materials = []  # 所有材料列表
        self.current_material = None  # 当前显示的材料
        self.display_history = []  # 显示历史
        self.materials_dir = os.path.join(os.path.expanduser("~"), "group_materials")
        
        # 确保材料目录存在
        os.makedirs(self.materials_dir, exist_ok=True)
        
        self.initUI()
        self.load_materials()
        
    def initUI(self):
        """初始化用户界面"""
        self.setWindowTitle("分组讨论材料展示")
        self.setMinimumSize(800, 600)
        
        main_layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧材料列表
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 材料列表标题
        list_title = QLabel("讨论材料")
        list_title.setFont(QFont("Arial", 12, QFont.Bold))
        left_layout.addWidget(list_title)
        
        # 材料列表
        self.material_list = QListWidget()
        self.material_list.setIconSize(QSize(48, 48))
        left_layout.addWidget(self.material_list)
        
        # 材料操作按钮
        btn_layout = QHBoxLayout()
        self.add_btn = QPushButton("添加材料")
        self.remove_btn = QPushButton("删除材料")
        self.share_btn = QPushButton("分享材料")
        
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.remove_btn)
        btn_layout.addWidget(self.share_btn)
        
        left_layout.addLayout(btn_layout)
        
        # 右侧材料显示
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 材料标题
        self.material_title = QLabel("未选择材料")
        self.material_title.setFont(QFont("Arial", 14, QFont.Bold))
        self.material_title.setAlignment(Qt.AlignCenter)
        right_layout.addWidget(self.material_title)
        
        # 材料信息
        info_layout = QHBoxLayout()
        self.student_label = QLabel("学生: ")
        self.type_label = QLabel("类型: ")
        info_layout.addWidget(self.student_label)
        info_layout.addStretch()
        info_layout.addWidget(self.type_label)
        
        right_layout.addLayout(info_layout)
        
        # 材料内容显示区域
        self.content_area = QScrollArea()
        self.content_area.setWidgetResizable(True)
        self.content_area.setFrameShape(QFrame.StyledPanel)
        
        # 默认内容
        default_content = QLabel("请从左侧列表选择材料进行查看")
        default_content.setAlignment(Qt.AlignCenter)
        self.content_area.setWidget(default_content)
        
        right_layout.addWidget(self.content_area, 1)
        
        # 材料描述
        self.description_label = QLabel("描述: ")
        right_layout.addWidget(self.description_label)
        
        # 添加到分割器
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([200, 600])
        
        # 连接信号
        self.material_list.currentItemChanged.connect(self.on_material_selected)
        self.add_btn.clicked.connect(self.add_material)
        self.remove_btn.clicked.connect(self.remove_material)
        self.share_btn.clicked.connect(self.share_material)
        self.material_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.material_list.customContextMenuRequested.connect(self.show_context_menu)
        
    def show_context_menu(self, position):
        """显示右键菜单"""
        menu = QMenu()
        
        view_action = menu.addAction("查看")
        share_action = menu.addAction("分享")
        edit_action = menu.addAction("编辑")
        delete_action = menu.addAction("删除")
        
        # 获取当前选中的项
        current_item = self.material_list.currentItem()
        if not current_item:
            return
            
        action = menu.exec_(self.material_list.mapToGlobal(position))
        
        if action == view_action:
            self.on_material_selected(current_item, None)
        elif action == share_action:
            self.share_material()
        elif action == edit_action:
            self.edit_material()
        elif action == delete_action:
            self.remove_material()
        
    def load_materials(self):
        """加载已有材料"""
        materials_file = os.path.join(self.materials_dir, "materials.json")
        if os.path.exists(materials_file):
            try:
                with open(materials_file, 'r', encoding='utf-8') as f:
                    materials_data = json.load(f)
                    
                for data in materials_data:
                    material = StudentMaterial.from_dict(data)
                    self.materials.append(material)
                    
                self.update_material_list()
            except Exception as e:
                QMessageBox.warning(self, "加载失败", f"加载材料失败: {str(e)}")
                
    def save_materials(self):
        """保存材料列表"""
        materials_file = os.path.join(self.materials_dir, "materials.json")
        try:
            materials_data = [material.to_dict() for material in self.materials]
            with open(materials_file, 'w', encoding='utf-8') as f:
                json.dump(materials_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            QMessageBox.warning(self, "保存失败", f"保存材料失败: {str(e)}")
            
    def update_material_list(self):
        """更新材料列表"""
        self.material_list.clear()
        
        for material in self.materials:
            item = QListWidgetItem(material.title)
            
            # 设置图标
            if material.thumbnail_path and os.path.exists(material.thumbnail_path):
                icon = QIcon(material.thumbnail_path)
            else:
                # 根据材料类型设置默认图标
                icon_map = {
                    MaterialType.IMAGE: "image-icon.png",
                    MaterialType.DOCUMENT: "document-icon.png",
                    MaterialType.VIDEO: "video-icon.png",
                    MaterialType.AUDIO: "audio-icon.png",
                    MaterialType.URL: "url-icon.png",
                    MaterialType.TEXT: "text-icon.png"
                }
                
                # 这里应该有一个图标目录，暂时使用默认图标
                icon = QIcon.fromTheme(icon_map.get(material.material_type, "text-plain"))
                
            item.setIcon(icon)
            item.setData(Qt.UserRole, material)
            self.material_list.addItem(item)
            
    def on_material_selected(self, current, previous):
        """材料被选中时的处理"""
        if not current:
            return
            
        material = current.data(Qt.UserRole)
        if not material:
            return
            
        self.current_material = material
        
        # 更新标题和信息
        self.material_title.setText(material.title)
        self.student_label.setText(f"学生: {material.student_name}")
        self.type_label.setText(f"类型: {material.material_type}")
        self.description_label.setText(f"描述: {material.description}")
        
        # 显示材料内容
        self.display_material(material)
        
        # 发送选中信号
        self.material_selected.emit(material.to_dict())
        
    def display_material(self, material):
        """显示材料内容"""
        # 清除当前内容
        if self.content_area.widget():
            self.content_area.widget().deleteLater()
            
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        
        if material.material_type == MaterialType.IMAGE:
            # 显示图片
            if os.path.exists(material.content_path):
                image_label = QLabel()
                pixmap = QPixmap(material.content_path)
                image_label.setPixmap(pixmap.scaled(
                    self.content_area.width() - 20, 
                    self.content_area.height() - 20,
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                ))
                image_label.setAlignment(Qt.AlignCenter)
                content_layout.addWidget(image_label)
            else:
                content_layout.addWidget(QLabel("图片文件不存在"))
                
        elif material.material_type == MaterialType.DOCUMENT:
            # 显示文档（简单文本预览）
            try:
                with open(material.content_path, 'r', encoding='utf-8') as f:
                    text = f.read()
                    
                text_label = QLabel(text)
                text_label.setWordWrap(True)
                content_layout.addWidget(text_label)
            except:
                content_layout.addWidget(QLabel("无法预览此文档，请使用外部程序打开"))
                
                # 添加打开按钮
                open_btn = QPushButton("打开文件")
                open_btn.clicked.connect(lambda: self.open_external_file(material.content_path))
                content_layout.addWidget(open_btn)
                
        elif material.material_type == MaterialType.VIDEO:
            # 显示视频
            video_widget = QVideoWidget()
            media_player = QMediaPlayer(None, QMediaPlayer.VideoSurface)
            media_player.setVideoOutput(video_widget)
            media_player.setMedia(QMediaContent(QUrl.fromLocalFile(material.content_path)))
            
            content_layout.addWidget(video_widget)
            
            # 控制按钮
            controls_layout = QHBoxLayout()
            play_btn = QPushButton("播放")
            pause_btn = QPushButton("暂停")
            stop_btn = QPushButton("停止")
            
            play_btn.clicked.connect(media_player.play)
            pause_btn.clicked.connect(media_player.pause)
            stop_btn.clicked.connect(media_player.stop)
            
            controls_layout.addWidget(play_btn)
            controls_layout.addWidget(pause_btn)
            controls_layout.addWidget(stop_btn)
            
            content_layout.addLayout(controls_layout)
            
        elif material.material_type == MaterialType.AUDIO:
            # 显示音频（只有控制按钮）
            media_player = QMediaPlayer()
            media_player.setMedia(QMediaContent(QUrl.fromLocalFile(material.content_path)))
            
            # 音频信息
            info_label = QLabel(f"音频文件: {os.path.basename(material.content_path)}")
            content_layout.addWidget(info_label)
            
            # 控制按钮
            controls_layout = QHBoxLayout()
            play_btn = QPushButton("播放")
            pause_btn = QPushButton("暂停")
            stop_btn = QPushButton("停止")
            
            play_btn.clicked.connect(media_player.play)
            pause_btn.clicked.connect(media_player.pause)
            stop_btn.clicked.connect(media_player.stop)
            
            controls_layout.addWidget(play_btn)
            controls_layout.addWidget(pause_btn)
            controls_layout.addWidget(stop_btn)
            
            content_layout.addLayout(controls_layout)
            
        elif material.material_type == MaterialType.URL:
            # 显示URL链接
            url_label = QLabel(f"<a href='{material.content_path}'>{material.content_path}</a>")
            url_label.setOpenExternalLinks(True)
            content_layout.addWidget(url_label)
            
        elif material.material_type == MaterialType.TEXT:
            # 显示文本内容
            text_label = QLabel(material.content_path)
            text_label.setWordWrap(True)
            content_layout.addWidget(text_label)
            
        else:
            content_layout.addWidget(QLabel("不支持的材料类型"))
            
        self.content_area.setWidget(content_widget)
        
    def open_external_file(self, file_path):
        """使用外部程序打开文件"""
        import subprocess
        import platform
        
        if platform.system() == 'Windows':
            os.startfile(file_path)
        elif platform.system() == 'Darwin':  # macOS
            subprocess.call(('open', file_path))
        else:  # Linux
            subprocess.call(('xdg-open', file_path))
            
    def add_material(self):
        """添加新材料"""
        dialog = MaterialEditDialog(self.materials_dir, self)
        if dialog.exec_() == QDialog.Accepted:
            material = dialog.get_material()
            self.materials.append(material)
            self.update_material_list()
            self.save_materials()
            
    def edit_material(self):
        """编辑材料"""
        current_item = self.material_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "提示", "请先选择要编辑的材料")
            return
            
        material = current_item.data(Qt.UserRole)
        dialog = MaterialEditDialog(self.materials_dir, self, material)
        
        if dialog.exec_() == QDialog.Accepted:
            updated_material = dialog.get_material()
            
            # 更新材料列表
            for i, m in enumerate(self.materials):
                if m.material_id == updated_material.material_id:
                    self.materials[i] = updated_material
                    break
                    
            self.update_material_list()
            self.save_materials()
            
            # 如果当前正在显示该材料，则更新显示
            if self.current_material and self.current_material.material_id == updated_material.material_id:
                self.current_material = updated_material
                self.on_material_selected(current_item, None)
                
    def remove_material(self):
        """删除材料"""
        current_item = self.material_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "提示", "请先选择要删除的材料")
            return
            
        material = current_item.data(Qt.UserRole)
        
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除材料 '{material.title}' 吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 从列表中移除
            self.materials = [m for m in self.materials if m.material_id != material.material_id]
            self.update_material_list()
            self.save_materials()
            
            # 如果有关联文件，询问是否删除
            if material.material_type in [MaterialType.IMAGE, MaterialType.DOCUMENT, MaterialType.VIDEO, MaterialType.AUDIO]:
                if os.path.exists(material.content_path):
                    file_reply = QMessageBox.question(
                        self, "删除文件", 
                        "是否同时删除关联的文件？",
                        QMessageBox.Yes | QMessageBox.No
                    )
                    
                    if file_reply == QMessageBox.Yes:
                        try:
                            os.remove(material.content_path)
                        except Exception as e:
                            QMessageBox.warning(self, "删除失败", f"删除文件失败: {str(e)}")
                            
            # 清除当前显示
            if self.current_material and self.current_material.material_id == material.material_id:
                self.current_material = None
                self.material_title.setText("未选择材料")
                self.student_label.setText("学生: ")
                self.type_label.setText("类型: ")
                self.description_label.setText("描述: ")
                
                default_content = QLabel("请从左侧列表选择材料进行查看")
                default_content.setAlignment(Qt.AlignCenter)
                self.content_area.setWidget(default_content)
                
    def share_material(self):
        """分享材料到大屏"""
        if not self.current_material:
            QMessageBox.warning(self, "提示", "请先选择要分享的材料")
            return
            
        # 发送分享信号
        self.material_shared.emit(self.current_material.to_dict())
        
        # 如果有socket_manager，发送到服务器
        if self.socket_manager:
            self.socket_manager.send_event('share_material', {
                'material': self.current_material.to_dict()
            })
            
            QMessageBox.information(self, "分享成功", f"材料 '{self.current_material.title}' 已分享到大屏")
        else:
            QMessageBox.warning(self, "分享失败", "未连接到服务器，无法分享材料")

class MaterialEditDialog(QDialog):
    """材料编辑对话框"""
    
    def __init__(self, materials_dir, parent=None, material=None):
        super().__init__(parent)
        self.materials_dir = materials_dir
        self.material = material
        self.content_path = material.content_path if material else ""
        self.thumbnail_path = material.thumbnail_path if material else ""
        self.material_type = material.material_type if material else MaterialType.TEXT
        
        self.initUI()
        
        if material:
            self.load_material(material)
            
    def initUI(self):
        """初始化界面"""
        self.setWindowTitle("编辑讨论材料")
        self.setMinimumSize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 基本信息
        form_layout = QGridLayout()
        
        # 标题
        form_layout.addWidget(QLabel("标题:"), 0, 0)
        self.title_edit = QLineEdit()
        form_layout.addWidget(self.title_edit, 0, 1)
        
        # 学生信息
        form_layout.addWidget(QLabel("学生ID:"), 1, 0)
        self.student_id_edit = QLineEdit()
        form_layout.addWidget(self.student_id_edit, 1, 1)
        
        form_layout.addWidget(QLabel("学生姓名:"), 2, 0)
        self.student_name_edit = QLineEdit()
        form_layout.addWidget(self.student_name_edit, 2, 1)
        
        # 材料类型
        form_layout.addWidget(QLabel("材料类型:"), 3, 0)
        self.type_combo = QComboBox()
        self.type_combo.addItem("文本", MaterialType.TEXT)
        self.type_combo.addItem("图片", MaterialType.IMAGE)
        self.type_combo.addItem("文档", MaterialType.DOCUMENT)
        self.type_combo.addItem("视频", MaterialType.VIDEO)
        self.type_combo.addItem("音频", MaterialType.AUDIO)
        self.type_combo.addItem("链接", MaterialType.URL)
        form_layout.addWidget(self.type_combo, 3, 1)
        
        layout.addLayout(form_layout)
        
        # 内容区域
        content_group = QGroupBox("材料内容")
        content_layout = QVBoxLayout(content_group)
        
        # 文本内容（默认）
        self.text_edit = QLineEdit()
        content_layout.addWidget(self.text_edit)
        
        # 文件选择
        file_layout = QHBoxLayout()
        self.file_path_label = QLabel("未选择文件")
        self.browse_btn = QPushButton("浏览...")
        file_layout.addWidget(self.file_path_label)
        file_layout.addWidget(self.browse_btn)
        
        content_layout.addLayout(file_layout)
        
        layout.addWidget(content_group)
        
        # 描述
        layout.addWidget(QLabel("描述:"))
        self.description_edit = QLineEdit()
        layout.addWidget(self.description_edit)
        
        # 按钮
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        # 连接信号
        self.type_combo.currentIndexChanged.connect(self.on_type_changed)
        self.browse_btn.clicked.connect(self.browse_file)
        
        # 初始化显示
        self.on_type_changed(0)
        
    def on_type_changed(self, index):
        """材料类型改变时的处理"""
        material_type = self.type_combo.currentData()
        
        # 显示/隐藏相应的控件
        if material_type == MaterialType.TEXT:
            self.text_edit.setVisible(True)
            self.file_path_label.setVisible(False)
            self.browse_btn.setVisible(False)
        elif material_type == MaterialType.URL:
            self.text_edit.setVisible(True)
            self.file_path_label.setVisible(False)
            self.browse_btn.setVisible(False)
        else:
            self.text_edit.setVisible(False)
            self.file_path_label.setVisible(True)
            self.browse_btn.setVisible(True)
            
    def browse_file(self):
        """浏览文件"""
        material_type = self.type_combo.currentData()
        
        file_filters = {
            MaterialType.IMAGE: "图片文件 (*.jpg *.jpeg *.png *.gif *.bmp)",
            MaterialType.DOCUMENT: "文档文件 (*.pdf *.doc *.docx *.txt *.ppt *.pptx)",
            MaterialType.VIDEO: "视频文件 (*.mp4 *.avi *.mov *.wmv *.flv)",
            MaterialType.AUDIO: "音频文件 (*.mp3 *.wav *.ogg *.flac)"
        }
        
        file_filter = file_filters.get(material_type, "所有文件 (*.*)")
        
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择文件", "", file_filter
        )
        
        if file_path:
            self.file_path_label.setText(os.path.basename(file_path))
            self.content_path = file_path
            
            # 如果是图片，可以同时设置为缩略图
            if material_type == MaterialType.IMAGE:
                self.thumbnail_path = file_path
                
    def load_material(self, material):
        """加载材料数据"""
        self.title_edit.setText(material.title)
        self.student_id_edit.setText(material.student_id)
        self.student_name_edit.setText(material.student_name)
        self.description_edit.setText(material.description)
        
        # 设置材料类型
        for i in range(self.type_combo.count()):
            if self.type_combo.itemData(i) == material.material_type:
                self.type_combo.setCurrentIndex(i)
                break
                
        # 设置内容
        if material.material_type in [MaterialType.TEXT, MaterialType.URL]:
            self.text_edit.setText(material.content_path)
        else:
            self.file_path_label.setText(os.path.basename(material.content_path))
            
    def get_material(self):
        """获取材料数据"""
        material_id = self.material.material_id if self.material else f"material_{int(time.time())}"
        material_type = self.type_combo.currentData()
        
        # 处理内容路径
        if material_type in [MaterialType.TEXT, MaterialType.URL]:
            content_path = self.text_edit.text()
        else:
            # 如果是文件，复制到材料目录
            if self.content_path and os.path.exists(self.content_path):
                file_name = os.path.basename(self.content_path)
                target_path = os.path.join(self.materials_dir, file_name)
                
                # 如果不是同一个文件，则复制
                if self.content_path != target_path:
                    shutil.copy2(self.content_path, target_path)
                    
                content_path = target_path
            else:
                content_path = self.content_path
                
        # 处理缩略图
        thumbnail_path = self.thumbnail_path
        if material_type == MaterialType.IMAGE and content_path:
            thumbnail_path = content_path
            
        return StudentMaterial(
            material_id,
            self.student_id_edit.text(),
            self.student_name_edit.text(),
            self.title_edit.text(),
            material_type,
            content_path,
            thumbnail_path,
            self.description_edit.text()
        )