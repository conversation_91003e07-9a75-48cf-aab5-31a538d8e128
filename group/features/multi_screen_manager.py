# group/features/multi_screen_manager.py
import asyncio
import threading
import vlc
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QFrame, QLabel, QGridLayout
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QFont, QImage, QPixmap
from aiortc import RTCPeerConnection, RTCSessionDescription
import requests

class WebRTCThread(QThread):
    """专门用于运行asyncio事件循环的线程"""
    new_frame_signal = pyqtSignal(QImage)

    def __init__(self, play_url):
        super().__init__()
        self.play_url = play_url
        self.loop = asyncio.new_event_loop()
        self.pc = RTCPeerConnection()
        self.is_running = True

    def run(self):
        """运行asyncio事件循环并处理WebRTC连接。"""
        asyncio.set_event_loop(self.loop)
        try:
            self.loop.create_task(self.start_webrtc())
            self.loop.run_forever()
        finally:
            print("事件循环已停止，正在清理...")
            tasks = asyncio.all_tasks(loop=self.loop)
            for task in tasks:
                task.cancel()
            group = asyncio.gather(*tasks, return_exceptions=True)
            self.loop.run_until_complete(group)
            self.loop.run_until_complete(self.pc.close())
            self.loop.close()
            print("WebRTC线程已清理完毕。")

    async def start_webrtc(self):
        """设置并管理WebRTC连接。"""
        @self.pc.on("track")
        async def on_track(track):
            print(f"收到轨道: {track.kind}")
            if track.kind == "video":
                while self.is_running:
                    try:
                        frame = await track.recv()
                        img = frame.to_ndarray(format="bgr24")
                        q_img = QImage(img.data, img.shape[1], img.shape[0], img.strides[0], QImage.Format_BGR888)
                        self.new_frame_signal.emit(q_img.copy())
                    except Exception as e:
                        print(f"处理视频帧时出错: {e}")
                        self.is_running = False
                        break
        
        try:
            self.pc.addTransceiver("video", direction="recvonly")
            offer = await self.pc.createOffer()
            await self.pc.setLocalDescription(offer)
            
            response = requests.post(self.play_url, 
                                     data=self.pc.localDescription.sdp,
                                     headers={'Content-Type': 'application/sdp'},
                                     timeout=10)
            response.raise_for_status()
            
            answer_sdp = response.text
            await self.pc.setRemoteDescription(RTCSessionDescription(sdp=answer_sdp, type="answer"))
            print("WebRTC 连接成功")

        except Exception as e:
            print(f"WebRTC 连接失败: {e}")
            if self.loop.is_running():
                self.loop.call_soon_threadsafe(self.loop.stop)

    def stop(self):
        """停止事件循环和WebRTC连接。"""
        self.is_running = False
        if self.loop.is_running():
            self.loop.call_soon_threadsafe(self.loop.stop)

class StudentStreamWidget(QFrame):
    """单个学员投屏显示组件"""
    def __init__(self, student_id, student_name, parent=None):
        super().__init__(parent)
        self.student_id = student_id
        self.student_name = student_name
        self.is_active = False
        self.webrtc_thread = None
        
        self.setFrameStyle(QFrame.Box)
        self.setLineWidth(2)
        
        layout = QVBoxLayout(self)
        
        self.info_label = QLabel(f"{student_name} ({student_id})")
        self.info_label.setAlignment(Qt.AlignCenter)
        self.info_label.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(self.info_label)
        
        self.video_widget = QLabel() # 使用QLabel显示视频帧
        self.video_widget.setAlignment(Qt.AlignCenter)
        self.video_widget.setStyleSheet("background-color: black;")
        layout.addWidget(self.video_widget, 1)
        
        self.status_label = QLabel("未连接")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: gray;")
        layout.addWidget(self.status_label)
    
    def start_stream(self, play_url):
        """开始播放学员流 (WebRTC)"""
        self.stop_stream() # 先停止之前的流
        
        self.webrtc_thread = WebRTCThread(play_url)
        self.webrtc_thread.new_frame_signal.connect(self.update_frame)
        self.webrtc_thread.start()
        
        self.is_active = True
        self.status_label.setText("正在投屏")
        self.status_label.setStyleSheet("color: green;")
        self.setStyleSheet("border: 2px solid green;")
    
    def update_frame(self, q_img):
        """更新视频帧"""
        pixmap = QPixmap.fromImage(q_img)
        self.video_widget.setPixmap(pixmap.scaled(
            self.video_widget.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation
        ))

    def stop_stream(self):
        """停止播放学员流"""
        if self.webrtc_thread:
            self.webrtc_thread.stop()
            self.webrtc_thread.wait()
            self.webrtc_thread = None
            
        self.is_active = False
        self.status_label.setText("未连接")
        self.status_label.setStyleSheet("color: gray;")
        self.setStyleSheet("border: 2px solid gray;")
        self.video_widget.clear()
        self.video_widget.setStyleSheet("background-color: black;")

class MultiScreenManager(QWidget):
    """多路投屏管理器"""
    student_stream_started = pyqtSignal(str, str)  # student_id, play_url
    student_stream_stopped = pyqtSignal(str)  # student_id
    
    def __init__(self, max_streams=6, parent=None):
        super().__init__(parent)
        self.max_streams = max_streams
        self.active_streams = {}  # student_id -> StudentStreamWidget
        self.available_slots = list(range(max_streams))
        
        self.initUI()
    
    def initUI(self):
        """初始化UI"""
        self.setWindowTitle("多路投屏显示")
        self.grid_layout = QGridLayout(self)
        
        self.stream_widgets = []
        rows = 2 if self.max_streams <= 6 else 3
        cols = 3 if self.max_streams <= 6 else 3
        
        for i in range(self.max_streams):
            row = i // cols
            col = i % cols
            widget = StudentStreamWidget(f"slot_{i}", f"位置 {i+1}")
            self.grid_layout.addWidget(widget, row, col)
            self.stream_widgets.append(widget)
    
    def add_student_stream(self, student_id, student_name, play_url):
        """添加学员投屏 (使用WebRTC)"""
        if len(self.active_streams) >= self.max_streams:
            print(f"已达到最大投屏数量限制 ({self.max_streams})")
            return False
        
        if student_id in self.active_streams:
            print(f"学员 {student_id} 已在投屏中")
            return False
        
        if not self.available_slots:
            print("没有可用的投屏位置")
            return False
        
        slot_index = self.available_slots.pop(0)
        widget = self.stream_widgets[slot_index]
        
        widget.student_id = student_id
        widget.student_name = student_name
        widget.info_label.setText(f"{student_name} ({student_id})")
        
        widget.start_stream(play_url)
        
        self.active_streams[student_id] = {
            'widget': widget,
            'slot_index': slot_index,
            'play_url': play_url
        }
        
        self.student_stream_started.emit(student_id, play_url)
        print(f"学员 {student_name} 开始投屏到位置 {slot_index + 1}")
        return True
    
    def remove_student_stream(self, student_id):
        """移除学员投屏"""
        if student_id not in self.active_streams:
            print(f"学员 {student_id} 未在投屏中")
            return False
        
        stream_info = self.active_streams.pop(student_id)
        widget = stream_info['widget']
        slot_index = stream_info['slot_index']
        
        widget.stop_stream()
        
        widget.student_id = f"slot_{slot_index}"
        widget.student_name = f"位置 {slot_index + 1}"
        widget.info_label.setText(f"位置 {slot_index + 1}")
        
        self.available_slots.append(slot_index)
        self.available_slots.sort()
        
        self.student_stream_stopped.emit(student_id)
        print(f"学员 {student_id} 停止投屏")
        return True
    
    def closeEvent(self, event):
        """关闭事件"""
        for student_id in list(self.active_streams.keys()):
            self.remove_student_stream(student_id)
        event.accept()
''
