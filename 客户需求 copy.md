#  场景

研讨型智慧教室。教室内有多个大屏，教师使用大屏替代传统黑板进行授课，多名学生围绕一个大屏组成一个小组

示意：

- 张老师的高等数学课：
    - 小组1：
        - 张三
        - 李四
        - 王五  
    - 小组2：
        - 赵六
        - 田七
    - 小组3：
        - 孙八
        - 吴九
        - 郑十

设备信息：
- 大屏：统信UOS系统
- 学生设备：手机、平板、电脑


# 开课方式和学生加入课堂方式
- 教师：
    - 使用数字码（4位、6位、9位）创建课程
    - 数字码可以转为二维码供学生扫描加入
    - 选择已创建的课程，可以显示该课程的数字码或二维码
- 学生：
    - 输入课堂数字码或扫码加入

# 教师的功能

## 课前

- 资源库：
    - 课件：可上传多种格式文件，支持基本文件操作
    - 习题：单选、多选、判断、填空、主观
    - 试卷：可导入多道习题或手动创建上述题型

- 创建课程安排

## 课中

- 广播
    - 大屏广播与画面接收功能：
        - 教师端大屏广播至小组端和学生端
        - 小组和学生画面：
            - 查看所有小组端和学生端画面，可选择任意个画面进行展示、对比
            - 可以暂停画面
    - 视频流要求：支持不少于75台终端的画面广播同屏，画质不低于1080P，且平均同屏延时应在1.5秒内

- 看板
    - 小组：
        - 当前教室分组
        - 小组端设备连接状态
        - 小组成员（学生）管理：
            - 拖拽分组
            - 随机分组
    - 学生考勤名单

- 与学生互动：
    - 向学生发送题目：
        - 来源：资源库习题或试卷，或课上临时创建题目，或截取屏幕作为临时题目
        - 结果：答题结束后自动统计客观题分数，显示主观题答案
        - 答题报告：
            - 答题结果实时显示柱状图
            - 柱状图可以按全班或分组切换展示
    - 文件分享：可以将资源库或本地文件发送给学生
    - 随机点名
    - 设置分组讨论主题和时间：
        - 教师可以截取任意内容作为讨论主题发送给各个小组和学生
        - 设置讨论倒计时

- 评分：
    - 教师根据学生的课堂表现对个人或小组进行加减分
    - 教师在随机点名互动后对选中的学生进行评分

## 课后
- 课堂报告（以时间顺序展示各个课堂活动）：
    - 签到人数
    - 题目与答题结果
    - 提问记录
    - 学生互动次数（指标是学生向老师发送内容的次数）
    - 支持备注
    - 评分
    - 选人情况

# 小组内的功能
- 小组大屏广播：
    - 广播到教师端大屏
    - 广播到其他小组端大屏
- 视频录制：对整个小组讨论过程进行视频录制
- 文件分享：小组内成员可以进行文件分享
- 白板应用：
    - 使用Excalidraw作为白板应用
    - 扩展小组内成员协作功能，要求小组成员能共同操作一个白板

# 学生的功能

## 课中
- 投屏：捕获摄像头或当前设备屏幕，投屏到小组或教师端大屏，可以同时显示多个画面来源
- 与教师互动：老师发送文字、 图片、文件
- 接收教师发送的题目并答题


## 课后
- 查看课堂报告
- 查找课中过程性资料：
    - 包含但不限于批注、板书、文件、快照等
    - 可按课程、资料类型、关键字进行筛选，可按时间排序




# 小组间的功能
投屏：小组的画面可以分享给其他小组

# 桌面教学工具（安装在大屏设备上）
- 画笔：
    - 粗细
    - 颜色
    - 撤销
    - 恢复
    - 清除
    - 截图
- 随机点名
- 触控：
    - 教师设备和小组设备间可以互相调起对方设备的虚拟键盘，并进行文字输入
    - 教师设备与小组设备间进行板书协作，各自显示屏上的书写内容可双向实时同步
- 开关机：教师设备可以控制小组设备开关机

- 教师端设备主副屏模式（教师端设备可能会连接2个屏幕，主屏用于显示课件内容，副屏用于显示教师工具）：
    - 主副屏可以展示不同的课件内容
    - 课件上下页联动
    - 一屏展示课件，另一屏启用白板应用进行书写
    - 屏幕广播，双屏展示的文档对象可进行画面广播，2个屏幕同时显示一个屏幕的内容



