#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Socket.IO连接的脚本
"""

import socketio
import time
import json

# 创建Socket.IO客户端
sio = socketio.Client()

@sio.event
def connect():
    print("✅ 已连接到服务器")

@sio.event
def disconnect():
    print("❌ 已断开连接")

@sio.event
def whiteboard_join_response(data):
    print(f"📝 收到白板加入响应: {data}")

@sio.event
def whiteboard_update(data):
    print(f"🔄 收到白板更新: {json.dumps(data, indent=2)}")

@sio.event
def participant_joined(data):
    print(f"👥 参与者加入: {data}")

@sio.event
def participant_left(data):
    print(f"👋 参与者离开: {data}")

def test_whiteboard_connection():
    """测试白板连接"""
    try:
        print("🚀 开始测试Socket.IO连接...")
        
        # 连接到服务器
        sio.connect('http://************:5000')
        
        # 等待连接建立
        time.sleep(1)
        
        # 模拟学生加入白板
        print("📋 发送白板加入请求...")
        sio.emit('collaborative_whiteboard_join', {
            'student_id': 'S001',
            'student_name': '张三',
            'group_id': '412462e9-b42f-47ca-acd8-3b0200ce0ee3',
            'course_schedule_id': 'CS_001'
        })
        
        # 等待响应
        time.sleep(2)
        
        # 发送测试更新
        print("🔄 发送测试白板更新...")
        sio.emit('whiteboard_update', {
            'session_id': 'test-session',  # 这里应该使用实际的session_id
            'user_id': 'S001',
            'elements': [{
                'id': 'test-element',
                'type': 'rectangle',
                'x': 100,
                'y': 100,
                'width': 200,
                'height': 100
            }],
            'appState': {
                'viewBackgroundColor': '#ffffff'
            }
        })
        
        # 保持连接一段时间以接收响应
        print("⏳ 等待响应...")
        time.sleep(5)
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
    finally:
        if sio.connected:
            sio.disconnect()
            print("🔌 已断开连接")

if __name__ == "__main__":
    test_whiteboard_connection()
