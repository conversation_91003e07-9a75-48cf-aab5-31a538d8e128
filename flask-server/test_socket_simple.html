<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Socket.IO 连接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .log { background-color: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 5px; max-height: 300px; overflow-y: auto; }
        button { padding: 10px 15px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <h1>Socket.IO 连接测试</h1>
    
    <div id="status" class="status disconnected">未连接</div>
    
    <div>
        <button onclick="testConnect()">测试连接</button>
        <button onclick="testJoinWhiteboard()">测试加入白板</button>
        <button onclick="testSendUpdate()">测试发送更新</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div id="log" class="log"></div>

    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
    <script>
        let socket = null;
        const statusDiv = document.getElementById('status');
        const logDiv = document.getElementById('log');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStatus(connected) {
            if (connected) {
                statusDiv.textContent = '已连接';
                statusDiv.className = 'status connected';
            } else {
                statusDiv.textContent = '未连接';
                statusDiv.className = 'status disconnected';
            }
        }

        function testConnect() {
            if (socket) {
                socket.disconnect();
                socket = null;
            }

            log('开始连接到服务器...');
            
            socket = io({
                transports: ['websocket', 'polling'],
                upgrade: true,
                rememberUpgrade: true,
                timeout: 20000,
                forceNew: true,
                reconnection: true,
                reconnectionDelay: 1000,
                reconnectionAttempts: 5
            });

            socket.on('connect', () => {
                log('✅ Socket.IO连接成功');
                updateStatus(true);
            });

            socket.on('disconnect', (reason) => {
                log(`❌ Socket.IO连接断开: ${reason}`);
                updateStatus(false);
            });

            socket.on('connect_error', (error) => {
                log(`❌ Socket.IO连接错误: ${error.message}`);
                updateStatus(false);
            });

            // 监听白板相关事件
            socket.on('whiteboard_join_response', (data) => {
                log(`📝 收到白板加入响应: ${JSON.stringify(data)}`);
            });

            socket.on('whiteboard_update', (data) => {
                log(`🔄 收到白板更新: ${JSON.stringify(data)}`);
            });

            socket.on('participant_joined', (data) => {
                log(`👥 参与者加入: ${JSON.stringify(data)}`);
            });

            socket.on('participant_left', (data) => {
                log(`👋 参与者离开: ${JSON.stringify(data)}`);
            });
        }

        function testJoinWhiteboard() {
            if (!socket || !socket.connected) {
                log('❌ 请先连接到服务器');
                return;
            }

            log('📋 发送白板加入请求...');
            socket.emit('collaborative_whiteboard_join', {
                student_id: 'S001',
                student_name: '张三',
                group_id: '412462e9-b42f-47ca-acd8-3b0200ce0ee3',
                course_schedule_id: 'CS_001'
            });
        }

        function testSendUpdate() {
            if (!socket || !socket.connected) {
                log('❌ 请先连接到服务器');
                return;
            }

            log('🔄 发送测试白板更新...');
            socket.emit('whiteboard_update', {
                session_id: 'test-session-id',
                user_id: 'S001',
                elements: [{
                    id: 'test-' + Date.now(),
                    type: 'rectangle',
                    x: Math.random() * 200,
                    y: Math.random() * 200,
                    width: 100,
                    height: 100
                }],
                appState: {
                    viewBackgroundColor: '#ffffff'
                }
            });
        }

        function clearLog() {
            logDiv.innerHTML = '';
        }

        // 页面加载时自动连接
        window.onload = function() {
            log('页面加载完成，准备测试Socket.IO连接');
        };
    </script>
</body>
</html>
